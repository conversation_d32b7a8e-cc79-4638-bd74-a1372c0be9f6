# TODO:
# 1. Добавить коммент + доки к инициализации СУБД, чтобы было понятнее
# 2. Подумать, как лучше прокидывать переменную версии Jatoba при инициализации. Может быть, хардкодить default в образе при его сборке?

# --------------------variables--------------------
# Специальные переменные
# Идентификатор развертывания сервиса
variable "instance_uuid" {
  type    = string
  default = ""
}

# Переменные, задаваемые пользователем или вычисляемые динамически
# Размещение с внешним или внутренним IP-адресом
variable "jatoba_placement" {
  type    = string
  default = "internal"
}

# Зона доступности
variable "az" {
  type    = string
  default = "GZ1"
}

variable "vm_flavor" {
  description = "Object with vkcs compute flavor (resorces type)."
  type        = string
}

# Объём системного диска
variable "root_disk_size" {
  description = "Number with vkcs compute instance volume size"
  type        = number
  default     = 10
}

# Тип системного диска
variable "root_disk_type" {
  description = "Volume type"
  type        = string
  default     = "ceph-ssd"
}

# Объём диска данных
variable "data_disk_size" {
  description = "Number with vkcs compute instance volume size"
  type        = number
  default     = 32
}

# Тип диска данных
variable "data_disk_type" {
  description = "Volume type"
  type        = string
  default     = "ceph-ssd"
}

# Подсеть для ВМ Jatoba
variable "ds_subnet" {
 description = "Datasource network name to get from cloud"
 type        = string
 default     = ""
}

# Порты доступа для группы безопасности
variable "ports" {
  type    = list(number)
  default = [
    22, 5432
  ]
  description = "ports for secgroup rule"
}

locals {
  # Сокращенный вариант instance_uuid
  short_name = substr(var.instance_uuid, 0, 8)
  # Генерация имени хоста
  hosts_name = "jatoba-${local.short_name}"
}

# Идентификатор образа сервиса
variable "image_uuid" {
  type        = string
  default     = "3bfa6d8c-3429-4fc3-b53c-35972989d06f"
  description = "jatoba image"
}

# ---------------------data------------------------
# Получение данных виртуальной сети
data "vkcs_networking_subnet" subnet {
  subnet_id = var.ds_subnet
}

# --------------------security group---------------
# Создание группы безопасности
resource "vkcs_networking_secgroup" "secgroup" {
  name = "jatoba-${local.short_name}"
  sdn  = data.vkcs_networking_subnet.subnet.sdn
}

# Правила для группы безопасности
resource "vkcs_networking_secgroup_rule" "rules" {
  count             = length(var.ports)
  # Определение направления применения правил — для входящих (ingress) или исходящих (egress) соединений
  direction         = "ingress"
  # Список портов доступа
  port_range_max    = element(var.ports, count.index)
  port_range_min    = element(var.ports, count.index)
  # Протокол доступа
  protocol          = "tcp"
  # Удаленный сетевой префикс
  remote_ip_prefix  = "0.0.0.0/0"
  # Индентификатор группы безопасности, для которой созданы правила
  security_group_id = vkcs_networking_secgroup.secgroup.id
  description       = "rule_tcp_${element(var.ports, count.index)}"
}

# --------------------network----------------------
# Привязка IP-адреса к порту
resource "vkcs_networking_port" "ports" {
  name               = "jatoba-${local.short_name}"
  admin_state_up     = "true"
  network_id         = data.vkcs_networking_subnet.subnet.network_id
  sdn                = data.vkcs_networking_subnet.subnet.sdn
  security_group_ids = [
    vkcs_networking_secgroup.secgroup.id
  ]
  fixed_ip {
    subnet_id = var.ds_subnet
  }
}

# --------------------keypair----------------------
# Создание ключевой пары
resource "ivkcs_ssh_keypair" "keypair" {}

# --------------------------vm---------------------
# Создание cloud-config конфигурации. Получение данных для инициализации агента на хосте
resource "ivkcs_user_data" "user_data" {
  # Идентификатор развертывания сервиса
  uuid      = var.instance_uuid
  hosts     = [local.hosts_name]
  target_os = "redos73"

  # Ключи для доступа по SSH
  ssh_authorized_keys = [
    ivkcs_ssh_keypair.keypair.public_key,
  ]
}

# ресурс, который создает compute instance (VM)
resource "vkcs_compute_instance" "jatoba" {
  name      = local.hosts_name
  flavor_id = var.vm_flavor
  security_groups   = [vkcs_networking_secgroup.secgroup.name]
  availability_zone = var.az

  block_device {
    source_type      = "volume"
    destination_type = "volume"
    boot_index       = 0
    uuid             = vkcs_blockstorage_volume.boot.id
  }

  # Применение cloud-config конфигурации для настройки ВМ. Установка агента
  user_data = ivkcs_user_data.user_data.user_data[0]
  # Прикрепление IP-адреса к ВМ
  network {
    port = vkcs_networking_port.ports.id
  }

  # Попытка остановки ВМ перед удалением
  stop_before_destroy = true
  # Тайм-аут создания ВМ
  timeouts {
    create = "10m"
  }
}

# --------------------volume-----------------------
# Создание root-диска
resource "vkcs_blockstorage_volume" "boot" {
  name              = "${local.short_name}-jatoba-boot"
  # Метаданные
  metadata          = { "sid" : "xaas", "product" : "jatoba" }
  # Идентификатор образа сервиса
  # image_id заранее собранный и загруженный image jatoba
  image_id          = var.image_uuid
  volume_type       = var.root_disk_type
  size              = var.root_disk_size
  availability_zone = var.az
}

# Создание диска данных
resource "vkcs_blockstorage_volume" "jatoba_data" {
  name              = "${local.short_name}-jatoba-data"
  # Метаданные
  metadata          = { "sid" : "xaas", "product" : "jatoba" }
  volume_type       = var.data_disk_type
  size              = var.data_disk_size
  availability_zone = var.az
}

# Присоединение диска данных к ВМ
resource "vkcs_compute_volume_attach" "attached" {
  instance_id = vkcs_compute_instance.jatoba.id
  volume_id   = vkcs_blockstorage_volume.jatoba_data.id
}

# -----------------------external------------------
# Получение пула внешних IP-адресов
resource "vkcs_networking_floatingip" "fips" {
  count = var.jatoba_placement == "external" ? 1 : 0
  pool  = data.vkcs_networking_subnet.subnet.sdn == "neutron" ? "ext-net" : "internet"
}

# Назначение ВМ внешнего IP-адреса
resource "vkcs_compute_floatingip_associate" "fip" {
  count       = length(vkcs_networking_floatingip.fips)
  floating_ip = vkcs_networking_floatingip.fips[count.index].address
  instance_id = vkcs_compute_instance.jatoba.id
}

# Создание A-записи в DNS облачной платформы
resource "ivkcs_dns" "jatoba" {
  count  = length(vkcs_networking_floatingip.fips)
  name   = "jatoba-${local.short_name}"
  domain = "xaas.msk.vkcs.cloud"
  ip     = vkcs_networking_floatingip.fips[count.index].address
}

locals {
  jatoba_url            = var.jatoba_placement == "external" ? "${ivkcs_dns.jatoba[0].name}.${ivkcs_dns.jatoba[0].domain}" : "Не задан (Jatoba развёрнута во внутренней сети)"
  jatoba_external_ip    = var.jatoba_placement == "external" ? "${ivkcs_dns.jatoba[0].ip}" : "Не задан (Jatoba развёрнута во внутренней сети)"
}

# --------------------agent-run--------------------
locals {
  # Стартовый скрипт для ресурса ivkcs_agent_exec.start
  jatoba_init_cmd = <<-EOT
#!/bin/bash

set -e

scripts/mount-data-disk.sh

ansible-playbook playbooks/init-jatoba.yml \
  --extra-vars "JATOBA_MAJOR_VERSION=6" \
EOT

  # Скрипт хелсчека Jatoba
  jatoba_healthcheck_script = <<-EOT
#!/bin/bash

exit 0;

EOT

  # Скрипт ресайза ФС диска данных Jatoba
  data_disk_resize_script = <<-EOT
#!/bin/bash

set -e

scripts/resize-data-disk.sh

EOT

}

resource "ivkcs_agent_exec" "jatoba-init" {
  hosts = [local.hosts_name]
  name  = "Init Jatoba"
  uuid  = var.instance_uuid
  step {
    index   = 1
    type    = "bash"
    content = local.jatoba_init_cmd
    options {
      timeout  = "5m"
      cwd      = "/opt"
      attempts = 1
    }
  }

  depends_on = [
    vkcs_compute_instance.jatoba,
    vkcs_compute_volume_attach.attached
  ]
}

resource "ivkcs_agent_exec" "data-disk-resize" {
  hosts = [local.hosts_name]
  name  = "Resize data disk"
  uuid  = var.instance_uuid
  step {
    index   = 1
    type    = "bash"
    content = local.data_disk_resize_script
    options {
      timeout  = "1m"
      cwd      = "/opt"
      attempts = 1
    }
  }

  lifecycle {
    replace_triggered_by = [
      vkcs_blockstorage_volume.jatoba_data
      ]
  }

  depends_on = [
    vkcs_compute_instance.jatoba,
    vkcs_compute_volume_attach.attached,
    vkcs_blockstorage_volume.jatoba_data
  ]
}

# --------------------monitoring-----------------

data "ivkcs_monitoring_user" "write" {}

resource "ivkcs_agent_exec" "monitoring" {
  hosts = [local.hosts_name]
  name  = "setup_monitoring"
  uuid  = var.instance_uuid

  step {
    index   = 1
    type    = "bash"
    content = <<-EOC
#!/bin/bash

# Создание файла global_tags.conf
cat <<EOT > /etc/telegraf/telegraf.d/global_tags.conf

# Глобальные теги
[global_tags]
  # ID ВМ
  vm_uuid = "${vkcs_compute_instance.jatoba.id}"
EOT

# Создание файла output.conf
cat <<EOT > /etc/telegraf/telegraf.d/output.conf

# Настройка output-плагина с помощью источника данных ivkcs_monitoring_user
[[outputs.mcs_metrics]]
  user_id    = "${data.ivkcs_monitoring_user.write.user_id}"
  password   = "${data.ivkcs_monitoring_user.write.password}"
  project_id = "${data.ivkcs_monitoring_user.write.project_id}"
  auth_url   = "${data.ivkcs_monitoring_user.write.auth_url}"
  namespace = "${data.ivkcs_monitoring_user.write.namespace}"
  endpoint  = "${data.ivkcs_monitoring_user.write.endpoint}"
EOT

# Назначение владельца и группы на директорию telegraf.d и файлы в ней
chown -R telegraf:telegraf /etc/telegraf/telegraf.d/

# Перезагрузка агента мониторинга и включение его в автозагузку хоста
systemctl restart telegraf
systemctl enable telegraf

EOC

    options {
      timeout  = "5m"
      attempts = 1
    }
  }

  timeouts {
    create = "10m"
  }
}

# --------------------healthcheck-----------------
resource "ivkcs_agent_check" "health" {
  hosts = ["HOST"]
  uuid  = var.instance_uuid
  # Мониторинг сервиса по порту
  port_health {
    # IP-адрес
    host = "127.0.0.1"
    # Порт
    port    = 5432
    # Периодичность мониторинга
    period  = "1m"
  }

  script_health {
    type = "bash"
    script = local.jatoba_healthcheck_script
    options {
    }
    # Периодичность мониторинга
    period  = "10s"
  }

  depends_on = [
    vkcs_compute_instance.jatoba,
    vkcs_compute_volume_attach.attached,
    ivkcs_agent_exec.jatoba-init
  ]
}

# --------------------outputs----------------------
# Вывод закрытого SSH-ключа для доступа к ВМ
output "keypair" {
  value     = ivkcs_ssh_keypair.keypair.private_key
  # Выходной параметр содержит чувствительные данные
  sensitive = true
}

output "os_user" {
  value     = "redos"
}

# Внутренний IP Jatoba
output "jatoba-internal-ip" {
  value = vkcs_compute_instance.jatoba.access_ip_v4
}

# Внешний IP Jatoba
output "jatoba-external-ip" {
  value   = local.jatoba_external_ip
}

# Домен Jatoba
output "jatoba-url" {
  value   = local.jatoba_url
}

# Порт Jatoba
output "jatoba-port" {
  value = "5432"
}

# Суперюзер СУБД
# Пароль суперюзера (сгенерировать динамически)
